#!/usr/bin/env python3
"""
Standalone Power Plant Annual Report Search Engine

This script provides a complete power plant search functionality without
requiring LangGraph or any web framework. It can be run directly from
the command line.

Usage:
    python power_plant_search_standalone.py "Palo Verde Nuclear Plant"
    python power_plant_search_standalone.py "Three Mile Island" --no-download
"""

import os
import sys
import argparse
from pathlib import Path

# Add the backend src directory to Python path
backend_src = Path(__file__).parent / "backend" / "src"
if backend_src.exists():
    sys.path.insert(0, str(backend_src))

from agent.power_plant_search import PowerPlantReportFinder, search_power_plant_reports
from dotenv import load_dotenv


def main():
    """Main function for standalone power plant search."""
    parser = argparse.ArgumentParser(
        description="Search for power plant annual reports",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python power_plant_search_standalone.py "Palo Verde Nuclear Plant"
    python power_plant_search_standalone.py "Three Mile Island" --no-download
    python power_plant_search_standalone.py "Diablo Canyon" --output-dir ./reports
        """
    )
    
    parser.add_argument(
        "plant_name",
        help="Name of the power plant to search for"
    )
    
    parser.add_argument(
        "--no-download",
        action="store_true",
        help="Only return webpage links, don't download PDFs"
    )
    
    parser.add_argument(
        "--output-dir",
        default="downloads",
        help="Directory to save downloaded files (default: downloads)"
    )
    
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Configure logging
    if args.verbose:
        import logging
        logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    print("🔍 Power Plant Annual Report Search Engine")
    print("=" * 50)
    print(f"Searching for: {args.plant_name}")
    print(f"Download PDFs: {not args.no_download}")
    print(f"Output directory: {args.output_dir}")
    print()
    
    try:
        # Check for API keys
        scraper_api_key = os.getenv("SCRAPER_API_KEY")

        if not scraper_api_key:
            print("⚠️  Warning: ScraperAPI key not found")
            print("   Set SCRAPER_API_KEY in .env file for better search functionality")
            print("   The search will use basic methods with limited functionality")
            print()

        # Perform the search
        result = search_power_plant_reports(
            plant_name=args.plant_name,
            download_pdfs=not args.no_download,
            scraper_api_key=scraper_api_key
        )
        
        # Display results
        print_results(result)
        
    except KeyboardInterrupt:
        print("\n❌ Search interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error during search: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def print_results(result):
    """Print search results in a formatted way."""
    plant_name = result.get("plant_name", "Unknown")
    status = result.get("status", "unknown")
    
    print("📊 SEARCH RESULTS")
    print("=" * 50)
    print(f"Plant Name: {plant_name}")
    print(f"Status: {status}")
    
    # Holding company
    holding_company = result.get("holding_company")
    if holding_company:
        print(f"Holding Company: {holding_company}")
    
    print()
    
    # Handle different status cases
    if status == "error":
        error_msg = result.get("error", "Unknown error")
        print(f"❌ Error: {error_msg}")
        return
    
    # Annual report pages
    annual_report_pages = result.get("annual_report_pages", [])
    if annual_report_pages:
        print(f"📄 ANNUAL REPORT WEBPAGES ({len(annual_report_pages)} found)")
        print("-" * 40)
        for i, page in enumerate(annual_report_pages, 1):
            title = page.get("title", "No title")
            link = page.get("link", "No link")
            print(f"{i}. {title}")
            print(f"   🔗 {link}")
            print()
    
    # PDF links
    pdf_links = result.get("pdf_links", [])
    if pdf_links:
        print(f"📁 PDF LINKS ({len(pdf_links)} found)")
        print("-" * 40)
        for i, pdf_link in enumerate(pdf_links, 1):
            print(f"{i}. {pdf_link}")
        print()
    
    # Downloaded files
    downloaded_files = result.get("downloaded_files", [])
    if downloaded_files:
        print(f"⬇️  DOWNLOADED FILES ({len(downloaded_files)} files)")
        print("-" * 40)
        for i, file_path in enumerate(downloaded_files, 1):
            file_size = get_file_size(file_path)
            print(f"{i}. {os.path.basename(file_path)} ({file_size})")
            print(f"   📁 {file_path}")
        print()
    
    # Summary
    if status == "completed":
        print("✅ Search completed successfully!")
        if downloaded_files:
            print(f"   Downloaded {len(downloaded_files)} files")
        if annual_report_pages:
            print(f"   Found {len(annual_report_pages)} webpage links")
    elif status == "no_pdfs_found":
        print("⚠️  No PDF files found, but webpage links are available")
    elif status == "webpage_links_only":
        print("ℹ️  Webpage links only (PDF download was disabled)")
    
    print()


def get_file_size(file_path):
    """Get human-readable file size."""
    try:
        size = os.path.getsize(file_path)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    except OSError:
        return "Unknown size"


def interactive_mode():
    """Run in interactive mode for multiple searches."""
    print("🔍 Power Plant Search Engine - Interactive Mode")
    print("=" * 50)
    print("Enter power plant names to search for annual reports.")
    print("Type 'quit' or 'exit' to stop.")
    print()
    
    load_dotenv()
    
    while True:
        try:
            plant_name = input("Enter power plant name: ").strip()
            
            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not plant_name:
                continue
            
            download_choice = input("Download PDFs? (y/n, default=y): ").strip().lower()
            download_pdfs = download_choice != 'n'
            
            print(f"\n🔍 Searching for: {plant_name}")
            print("-" * 30)
            
            result = search_power_plant_reports(
                plant_name=plant_name,
                download_pdfs=download_pdfs,
                scraper_api_key=os.getenv("SCRAPER_API_KEY")
            )
            
            print_results(result)
            print("=" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print()


if __name__ == "__main__":
    # Check if running in interactive mode
    if len(sys.argv) == 1:
        interactive_mode()
    else:
        main()
