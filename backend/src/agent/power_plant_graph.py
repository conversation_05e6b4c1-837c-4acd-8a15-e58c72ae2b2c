"""
Power Plant Search Graph

This module creates a specialized LangGraph for power plant annual report searches.
It integrates the PowerPlantReportFinder with the LangGraph framework.
"""

import os
import re
from typing import Dict, Any

from agent.tools_and_schemas import PowerPlantSearchRequest, PowerPlantSearchResult
from agent.power_plant_search import PowerPlantReportFinder
from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import RunnableConfig
from langchain_google_genai import ChatGoogleGenerativeAI

from agent.state import OverallState
from agent.configuration import Configuration
from agent.prompts import get_current_date

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")


def detect_power_plant_query(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Detect if the user query is asking for power plant annual reports.
    
    Args:
        state: Current graph state containing the user's messages
        config: Configuration for the runnable
        
    Returns:
        Dictionary with routing decision and extracted plant name if applicable
    """
    configurable = Configuration.from_runnable_config(config)
    
    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if msg.type == "human"]
    if not user_messages:
        return {"route": "general_search", "power_plant_search_request": None}
    
    latest_message = user_messages[-1].content.lower()
    
    # Keywords that indicate power plant search
    power_plant_keywords = [
        "power plant", "powerplant", "power station", "power facility",
        "nuclear plant", "coal plant", "gas plant", "solar plant", "wind farm",
        "hydroelectric", "thermal plant", "generating station"
    ]
    
    annual_report_keywords = [
        "annual report", "annual reports", "financial report", "financial statements",
        "investor relations", "sec filing", "10-k", "form 10-k"
    ]
    
    # Check if message contains both power plant and annual report keywords
    has_power_plant = any(keyword in latest_message for keyword in power_plant_keywords)
    has_annual_report = any(keyword in latest_message for keyword in annual_report_keywords)
    
    if has_power_plant and has_annual_report:
        # Extract plant name using LLM
        plant_name = extract_plant_name(latest_message, configurable)
        if plant_name:
            return {
                "route": "power_plant_search",
                "power_plant_search_request": {
                    "plant_name": plant_name,
                    "download_pdfs": True
                }
            }
    
    return {"route": "general_search", "power_plant_search_request": None}


def extract_plant_name(message: str, config: Configuration) -> str:
    """
    Extract power plant name from user message using LLM.
    
    Args:
        message: User message
        config: Configuration object
        
    Returns:
        Extracted plant name or None
    """
    llm = ChatGoogleGenerativeAI(
        model=config.query_generator_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    prompt = f"""
    Extract the power plant name from the following user message. 
    Return only the plant name, nothing else. If no specific plant name is mentioned, return "NONE".
    
    User message: {message}
    
    Power plant name:
    """
    
    try:
        response = llm.invoke(prompt)
        plant_name = response.content.strip()
        return plant_name if plant_name != "NONE" else None
    except Exception:
        return None


def power_plant_search(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Perform power plant annual report search.
    
    Args:
        state: Current graph state
        config: Configuration for the runnable
        
    Returns:
        Dictionary with search results
    """
    request = state.get("power_plant_search_request")
    if not request:
        return {
            "power_plant_search_result": {
                "status": "error",
                "error": "No search request provided"
            }
        }
    
    try:
        # Initialize the power plant search engine
        finder = PowerPlantReportFinder(
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            search_engine_id=os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        )
        
        # Perform the search
        result = finder.find_and_download_reports(request["plant_name"])
        
        return {"power_plant_search_result": result}
        
    except Exception as e:
        return {
            "power_plant_search_result": {
                "status": "error",
                "error": str(e),
                "plant_name": request.get("plant_name", "Unknown")
            }
        }


def format_power_plant_response(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Format the power plant search results into a user-friendly response.
    
    Args:
        state: Current graph state
        config: Configuration for the runnable
        
    Returns:
        Dictionary with formatted response message
    """
    result = state.get("power_plant_search_result", {})
    plant_name = result.get("plant_name", "Unknown")
    status = result.get("status", "unknown")
    
    if status == "error":
        error_msg = result.get("error", "Unknown error occurred")
        response_content = f"""
I encountered an error while searching for annual reports for {plant_name}:

**Error:** {error_msg}

Please try again or provide a more specific power plant name.
"""
    elif status == "completed":
        holding_company = result.get("holding_company")
        pdf_links = result.get("pdf_links", [])
        downloaded_files = result.get("downloaded_files", [])
        annual_report_pages = result.get("annual_report_pages", [])
        
        response_content = f"""
# Power Plant Annual Report Search Results

**Power Plant:** {plant_name}
"""
        
        if holding_company:
            response_content += f"**Holding Company:** {holding_company}\n"
        
        if annual_report_pages:
            response_content += f"\n## Annual Report Webpage Links ({len(annual_report_pages)} found):\n"
            for i, page in enumerate(annual_report_pages[:5], 1):  # Limit to top 5
                response_content += f"{i}. [{page.get('title', 'Annual Reports Page')}]({page.get('link')})\n"
        
        if pdf_links:
            response_content += f"\n## PDF Links Found ({len(pdf_links)} total):\n"
            for i, pdf_link in enumerate(pdf_links[:10], 1):  # Limit to top 10
                response_content += f"{i}. [Download PDF]({pdf_link})\n"
        
        if downloaded_files:
            response_content += f"\n## Downloaded Files ({len(downloaded_files)} files):\n"
            for i, file_path in enumerate(downloaded_files, 1):
                filename = os.path.basename(file_path)
                response_content += f"{i}. {filename}\n"
        
        if not annual_report_pages and not pdf_links:
            response_content += "\n⚠️ No annual reports found for this power plant. You may want to try:\n"
            response_content += "- Checking the exact spelling of the power plant name\n"
            response_content += "- Searching for the parent/holding company name\n"
            response_content += "- Looking for alternative names the plant might be known by\n"
            
    elif status == "no_pdfs_found":
        annual_report_pages = result.get("annual_report_pages", [])
        response_content = f"""
# Power Plant Annual Report Search Results

**Power Plant:** {plant_name}

I found some relevant pages but no direct PDF links. Here are the webpage links where annual reports might be available:

"""
        if annual_report_pages:
            for i, page in enumerate(annual_report_pages, 1):
                response_content += f"{i}. [{page.get('title', 'Annual Reports Page')}]({page.get('link')})\n"
        else:
            response_content += "No relevant pages found. Please check the power plant name and try again."
    
    else:
        response_content = f"Search for {plant_name} is in progress..."
    
    # Create AI message with the formatted response
    ai_message = AIMessage(content=response_content)
    
    return {"messages": [ai_message]}


def route_after_detection(state: OverallState) -> str:
    """
    Route to appropriate node based on detection results.
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    route = state.get("route", "general_search")
    if route == "power_plant_search":
        return "power_plant_search"
    else:
        return "general_search_fallback"


def general_search_fallback(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Fallback for general search queries that aren't power plant specific.
    
    Args:
        state: Current graph state
        config: Configuration for the runnable
        
    Returns:
        Dictionary with fallback response
    """
    response_content = """
This agent is specialized for finding power plant annual reports. 

To use this service, please ask about annual reports for a specific power plant, for example:
- "Find annual reports for Palo Verde Nuclear Plant"
- "Get financial statements for Three Mile Island power plant"
- "Search for investor relations documents for Diablo Canyon"

For general web research, please use the main research agent.
"""
    
    ai_message = AIMessage(content=response_content)
    return {"messages": [ai_message]}


# Create the Power Plant Search Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Add nodes
builder.add_node("detect_power_plant_query", detect_power_plant_query)
builder.add_node("power_plant_search", power_plant_search)
builder.add_node("format_power_plant_response", format_power_plant_response)
builder.add_node("general_search_fallback", general_search_fallback)

# Set entry point
builder.add_edge(START, "detect_power_plant_query")

# Add conditional routing
builder.add_conditional_edges(
    "detect_power_plant_query",
    route_after_detection,
    ["power_plant_search", "general_search_fallback"]
)

# Connect power plant search flow
builder.add_edge("power_plant_search", "format_power_plant_response")
builder.add_edge("format_power_plant_response", END)

# Connect fallback flow
builder.add_edge("general_search_fallback", END)

# Compile the graph
power_plant_graph = builder.compile(name="power-plant-search-agent")
