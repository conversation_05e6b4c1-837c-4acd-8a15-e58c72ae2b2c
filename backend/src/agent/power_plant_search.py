"""
Power Plant Annual Report Search Engine

This module provides functionality to search for power plant annual reports
through a multi-step process:
1. Direct search for power plant annual reports
2. If not found, search for holding company
3. Find holding company's investor relations pages
4. Extract annual report webpage links
5. Use Selenium to parse HTML and find PDF links
6. Download annual reports
"""

import os
import re
import time
import requests
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PowerPlantSearchEngine:
    """Main class for searching power plant annual reports."""

    def __init__(self, scraper_api_key: str = None):
        """
        Initialize the search engine.

        Args:
            scraper_api_key: <PERSON><PERSON><PERSON><PERSON><PERSON> key for web scraping and search
        """
        self.scraper_api_key = scraper_api_key or os.getenv("SCRAPER_API_KEY")
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def search_google(self, query: str, num_results: int = 10) -> List[Dict]:
        """
        Search Google using ScraperAPI.

        Args:
            query: Search query
            num_results: Number of results to return

        Returns:
            List of search results with title, link, and snippet
        """
        if not self.scraper_api_key:
            logger.warning("ScraperAPI key not configured, using basic search")
            return self._basic_search(query, num_results)

        # Use ScraperAPI's Google search endpoint
        url = "https://api.scraperapi.com/structured/google/search"
        params = {
            'api_key': self.scraper_api_key,
            'query': query,
            'num': min(num_results, 10)
        }

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            # ScraperAPI returns results in 'organic_results' field
            for item in data.get('organic_results', []):
                results.append({
                    'title': item.get('title', ''),
                    'link': item.get('link', ''),
                    'snippet': item.get('snippet', '')
                })
            return results

        except Exception as e:
            logger.error(f"ScraperAPI search failed: {e}")
            return self._basic_search(query, num_results)

    def _basic_search(self, query: str, num_results: int) -> List[Dict]:
        """Basic search method when ScraperAPI is not available."""
        logger.info(f"Using basic search for: {query}")

        # Try to construct some basic search URLs
        search_urls = [
            f"https://www.google.com/search?q={query.replace(' ', '+')}",
            f"https://duckduckgo.com/?q={query.replace(' ', '+')}",
        ]

        results = []
        for url in search_urls:
            results.append({
                'title': f"Search for: {query}",
                'link': url,
                'snippet': f"Manual search required for: {query}"
            })

        return results[:num_results]
    
    def search_power_plant_reports(self, plant_name: str) -> List[Dict]:
        """
        Search for annual reports of a specific power plant.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            List of search results containing potential annual reports
        """
        queries = [
            f'"{plant_name}" power plant annual report',
            f'"{plant_name}" annual report financial statements',
            f'"{plant_name}" power plant investor relations',
            f'{plant_name} power plant annual report filetype:pdf'
        ]
        
        all_results = []
        for query in queries:
            logger.info(f"Searching: {query}")
            results = self.search_google(query, 5)
            all_results.extend(results)
            time.sleep(1)  # Rate limiting
            
        return self._deduplicate_results(all_results)
    
    def search_holding_company(self, plant_name: str) -> Optional[str]:
        """
        Search for the holding company of a power plant.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Name of the holding company if found
        """
        queries = [
            f'"{plant_name}" power plant holding company',
            f'"{plant_name}" power plant parent company',
            f'"{plant_name}" power plant owner company',
            f'"{plant_name}" power plant operated by'
        ]
        
        for query in queries:
            logger.info(f"Searching for holding company: {query}")
            results = self.search_google(query, 5)
            
            # Extract potential company names from results
            company_name = self._extract_company_name(results, plant_name)
            if company_name:
                return company_name
                
            time.sleep(1)  # Rate limiting
            
        return None
    
    def _extract_company_name(self, results: List[Dict], plant_name: str) -> Optional[str]:
        """
        Extract company name from search results.
        
        Args:
            results: Search results
            plant_name: Original plant name for context
            
        Returns:
            Extracted company name if found
        """
        # Common patterns for company names
        company_patterns = [
            r'(\w+(?:\s+\w+)*)\s+(?:Corporation|Corp|Company|Co|Inc|LLC|Ltd|Limited|Holdings|Energy|Power|Electric|Utilities)',
            r'(\w+(?:\s+\w+)*)\s+(?:owns|operates|manages)\s+' + re.escape(plant_name),
            r'owned\s+by\s+(\w+(?:\s+\w+)*)',
            r'operated\s+by\s+(\w+(?:\s+\w+)*)'
        ]
        
        for result in results:
            text = f"{result.get('title', '')} {result.get('snippet', '')}"
            
            for pattern in company_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    # Return the first reasonable match
                    company = matches[0].strip()
                    if len(company) > 3 and company.lower() != plant_name.lower():
                        return company
                        
        return None
    
    def search_company_annual_reports(self, company_name: str) -> List[Dict]:
        """
        Search for annual reports of a company.
        
        Args:
            company_name: Name of the company
            
        Returns:
            List of search results containing annual report pages
        """
        queries = [
            f'"{company_name}" annual reports investor relations',
            f'"{company_name}" financial statements annual reports',
            f'site:{self._get_company_domain(company_name)} annual reports',
            f'"{company_name}" SEC filings annual report',
            f'"{company_name}" investor relations financials'
        ]
        
        all_results = []
        for query in queries:
            logger.info(f"Searching company reports: {query}")
            results = self.search_google(query, 5)
            all_results.extend(results)
            time.sleep(1)  # Rate limiting
            
        return self._deduplicate_results(all_results)
    
    def _get_company_domain(self, company_name: str) -> str:
        """
        Attempt to guess company domain from name.
        
        Args:
            company_name: Company name
            
        Returns:
            Guessed domain name
        """
        # Simple heuristic - take first word and add .com
        first_word = company_name.split()[0].lower()
        return f"{first_word}.com"
    
    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """
        Remove duplicate results based on URL.
        
        Args:
            results: List of search results
            
        Returns:
            Deduplicated results
        """
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get('link', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
                
        return unique_results
    
    def filter_investor_relations_pages(self, results: List[Dict]) -> List[Dict]:
        """
        Filter results to find investor relations or financial pages.
        
        Args:
            results: Search results
            
        Returns:
            Filtered results likely to contain annual reports
        """
        ir_keywords = [
            'investor', 'financial', 'annual', 'report', 'sec', 'filing',
            'earnings', 'statements', 'disclosure', 'governance'
        ]
        
        filtered_results = []
        for result in results:
            url = result.get('link', '').lower()
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            
            # Check if URL or content contains IR keywords
            text_to_check = f"{url} {title} {snippet}"
            if any(keyword in text_to_check for keyword in ir_keywords):
                filtered_results.append(result)
                
        return filtered_results


class WebPageScraper:
    """Class for scraping web pages to find annual report links."""

    def __init__(self, headless: bool = True):
        """
        Initialize the web scraper.

        Args:
            headless: Whether to run browser in headless mode
        """
        self.headless = headless
        self.driver = None

    def setup_driver(self):
        """Setup Selenium WebDriver."""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            return True
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            return False

    def close_driver(self):
        """Close the WebDriver."""
        if self.driver:
            self.driver.quit()
            self.driver = None

    def scrape_annual_report_links(self, url: str) -> List[Dict]:
        """
        Scrape a webpage for annual report links.

        Args:
            url: URL to scrape

        Returns:
            List of annual report links with metadata
        """
        if not self.driver and not self.setup_driver():
            return []

        try:
            logger.info(f"Scraping: {url}")
            self.driver.get(url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Find all links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            annual_report_links = []

            for link in links:
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()

                    if self._is_annual_report_link(href, text):
                        annual_report_links.append({
                            'url': href,
                            'text': text,
                            'type': self._classify_link_type(href, text)
                        })

                except Exception as e:
                    continue

            return annual_report_links

        except TimeoutException:
            logger.error(f"Timeout loading page: {url}")
            return []
        except WebDriverException as e:
            logger.error(f"WebDriver error scraping {url}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return []

    def _is_annual_report_link(self, href: str, text: str) -> bool:
        """
        Check if a link is likely an annual report.

        Args:
            href: Link URL
            text: Link text

        Returns:
            True if likely an annual report link
        """
        if not href:
            return False

        # Check for PDF links
        if href.lower().endswith('.pdf'):
            annual_keywords = ['annual', 'report', '10-k', 'form 10-k']
            combined_text = f"{href} {text}".lower()
            return any(keyword in combined_text for keyword in annual_keywords)

        # Check for webpage links to annual reports
        annual_keywords = [
            'annual report', 'annual reports', '10-k', 'form 10-k',
            'financial statements', 'investor relations', 'sec filings'
        ]

        combined_text = f"{href} {text}".lower()
        return any(keyword in combined_text for keyword in annual_keywords)

    def _classify_link_type(self, href: str, text: str) -> str:
        """
        Classify the type of annual report link.

        Args:
            href: Link URL
            text: Link text

        Returns:
            Link type classification
        """
        if href.lower().endswith('.pdf'):
            return 'pdf'
        elif 'investor' in f"{href} {text}".lower():
            return 'investor_relations'
        elif 'sec' in f"{href} {text}".lower():
            return 'sec_filing'
        else:
            return 'webpage'

    def scrape_pdf_links_from_page(self, url: str) -> List[str]:
        """
        Scrape a webpage specifically for PDF links.

        Args:
            url: URL to scrape

        Returns:
            List of PDF URLs
        """
        if not self.driver and not self.setup_driver():
            return []

        try:
            logger.info(f"Scraping PDF links from: {url}")
            self.driver.get(url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Find all PDF links
            pdf_links = []
            links = self.driver.find_elements(By.TAG_NAME, "a")

            for link in links:
                try:
                    href = link.get_attribute("href")
                    if href and href.lower().endswith('.pdf'):
                        # Convert relative URLs to absolute
                        if href.startswith('/'):
                            base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
                            href = urljoin(base_url, href)
                        elif not href.startswith('http'):
                            href = urljoin(url, href)

                        pdf_links.append(href)

                except Exception:
                    continue

            return list(set(pdf_links))  # Remove duplicates

        except Exception as e:
            logger.error(f"Error scraping PDF links from {url}: {e}")
            return []


class PDFDownloader:
    """Class for downloading PDF files."""

    def __init__(self, download_dir: str = "downloads"):
        """
        Initialize PDF downloader.

        Args:
            download_dir: Directory to save downloaded files
        """
        self.download_dir = download_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Create download directory
        os.makedirs(download_dir, exist_ok=True)

    def download_pdf(self, url: str, filename: str = None) -> Optional[str]:
        """
        Download a PDF file.

        Args:
            url: URL of the PDF
            filename: Optional custom filename

        Returns:
            Path to downloaded file if successful
        """
        try:
            logger.info(f"Downloading PDF: {url}")

            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Generate filename if not provided
            if not filename:
                filename = self._generate_filename(url, response)

            filepath = os.path.join(self.download_dir, filename)

            # Download file
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.info(f"Downloaded: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Failed to download {url}: {e}")
            return None

    def _generate_filename(self, url: str, response: requests.Response) -> str:
        """
        Generate filename for downloaded PDF.

        Args:
            url: PDF URL
            response: HTTP response object

        Returns:
            Generated filename
        """
        # Try to get filename from Content-Disposition header
        content_disposition = response.headers.get('content-disposition', '')
        if 'filename=' in content_disposition:
            filename = content_disposition.split('filename=')[1].strip('"')
            if filename.endswith('.pdf'):
                return filename

        # Generate from URL
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)

        if not filename or not filename.endswith('.pdf'):
            filename = f"annual_report_{int(time.time())}.pdf"

        return filename

    def download_multiple_pdfs(self, urls: List[str], plant_name: str) -> List[str]:
        """
        Download multiple PDF files.

        Args:
            urls: List of PDF URLs
            plant_name: Name of power plant for organizing files

        Returns:
            List of successfully downloaded file paths
        """
        # Create subdirectory for this plant
        plant_dir = os.path.join(self.download_dir, plant_name.replace(' ', '_'))
        os.makedirs(plant_dir, exist_ok=True)

        downloaded_files = []
        for i, url in enumerate(urls):
            filename = f"{plant_name.replace(' ', '_')}_report_{i+1}.pdf"
            filepath = os.path.join(plant_dir, filename)

            try:
                response = self.session.get(url, stream=True, timeout=30)
                response.raise_for_status()

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                downloaded_files.append(filepath)
                logger.info(f"Downloaded: {filepath}")

            except Exception as e:
                logger.error(f"Failed to download {url}: {e}")

        return downloaded_files


class PowerPlantReportFinder:
    """Main orchestrator class for finding power plant annual reports."""

    def __init__(self, scraper_api_key: str = None, download_dir: str = "downloads"):
        """
        Initialize the report finder.

        Args:
            scraper_api_key: ScraperAPI key for web scraping and search
            download_dir: Directory for downloads
        """
        self.search_engine = PowerPlantSearchEngine(scraper_api_key)
        self.scraper = WebPageScraper()
        self.downloader = PDFDownloader(download_dir)

    def find_and_download_reports(self, plant_name: str) -> Dict:
        """
        Complete workflow to find and download power plant annual reports.

        Args:
            plant_name: Name of the power plant

        Returns:
            Dictionary with results and downloaded files
        """
        results = {
            'plant_name': plant_name,
            'holding_company': None,
            'search_results': [],
            'annual_report_pages': [],
            'pdf_links': [],
            'downloaded_files': [],
            'status': 'started'
        }

        try:
            # Step 1: Search for power plant annual reports directly
            logger.info(f"Step 1: Searching for {plant_name} annual reports")
            direct_results = self.search_engine.search_power_plant_reports(plant_name)
            results['search_results'].extend(direct_results)

            # Check if we found direct PDF links
            pdf_results = [r for r in direct_results if r.get('link', '').lower().endswith('.pdf')]
            if pdf_results:
                logger.info(f"Found {len(pdf_results)} direct PDF links")
                for pdf_result in pdf_results:
                    results['pdf_links'].append(pdf_result['link'])

            # Step 2: If no direct results, search for holding company
            if not pdf_results:
                logger.info(f"Step 2: Searching for {plant_name} holding company")
                holding_company = self.search_engine.search_holding_company(plant_name)

                if holding_company:
                    results['holding_company'] = holding_company
                    logger.info(f"Found holding company: {holding_company}")

                    # Step 3: Search for holding company annual reports
                    logger.info(f"Step 3: Searching for {holding_company} annual reports")
                    company_results = self.search_engine.search_company_annual_reports(holding_company)
                    results['search_results'].extend(company_results)

                    # Filter for investor relations pages
                    ir_pages = self.search_engine.filter_investor_relations_pages(company_results)
                    results['annual_report_pages'] = ir_pages

                    # Step 4: Scrape investor relations pages for PDF links
                    if ir_pages:
                        logger.info(f"Step 4: Scraping {len(ir_pages)} investor relations pages")
                        for page in ir_pages[:3]:  # Limit to top 3 pages
                            page_pdf_links = self.scraper.scrape_pdf_links_from_page(page['link'])
                            results['pdf_links'].extend(page_pdf_links)

                            # Also look for annual report links
                            annual_links = self.scraper.scrape_annual_report_links(page['link'])
                            for link in annual_links:
                                if link['type'] == 'pdf':
                                    results['pdf_links'].append(link['url'])
                                elif link['type'] == 'webpage':
                                    # Scrape this webpage for PDFs too
                                    nested_pdfs = self.scraper.scrape_pdf_links_from_page(link['url'])
                                    results['pdf_links'].extend(nested_pdfs)

            # Step 5: Download PDFs
            if results['pdf_links']:
                # Remove duplicates
                unique_pdf_links = list(set(results['pdf_links']))
                results['pdf_links'] = unique_pdf_links

                logger.info(f"Step 5: Downloading {len(unique_pdf_links)} PDF files")
                downloaded_files = self.downloader.download_multiple_pdfs(
                    unique_pdf_links, plant_name
                )
                results['downloaded_files'] = downloaded_files
                results['status'] = 'completed'
            else:
                results['status'] = 'no_pdfs_found'

        except Exception as e:
            logger.error(f"Error in find_and_download_reports: {e}")
            results['status'] = 'error'
            results['error'] = str(e)

        finally:
            # Clean up
            self.scraper.close_driver()

        return results

    def get_annual_report_webpage_links(self, plant_name: str) -> List[Dict]:
        """
        Get webpage links that contain annual reports (without downloading PDFs).

        Args:
            plant_name: Name of the power plant

        Returns:
            List of webpage links containing annual reports
        """
        try:
            # Search for power plant reports
            direct_results = self.search_engine.search_power_plant_reports(plant_name)

            # Search for holding company if needed
            holding_company = self.search_engine.search_holding_company(plant_name)
            company_results = []

            if holding_company:
                company_results = self.search_engine.search_company_annual_reports(holding_company)

            # Combine and filter results
            all_results = direct_results + company_results
            ir_pages = self.search_engine.filter_investor_relations_pages(all_results)

            # Add metadata
            for page in ir_pages:
                page['plant_name'] = plant_name
                page['holding_company'] = holding_company

            return ir_pages

        except Exception as e:
            logger.error(f"Error getting webpage links: {e}")
            return []

    def __del__(self):
        """Cleanup when object is destroyed."""
        if hasattr(self, 'scraper'):
            self.scraper.close_driver()


# Convenience function for easy usage
def search_power_plant_reports(plant_name: str, download_pdfs: bool = True,
                             scraper_api_key: str = None) -> Dict:
    """
    Convenience function to search for power plant annual reports.

    Args:
        plant_name: Name of the power plant
        download_pdfs: Whether to download PDF files
        scraper_api_key: ScraperAPI key for web scraping and search

    Returns:
        Dictionary with search results and downloaded files
    """
    finder = PowerPlantReportFinder(scraper_api_key)

    if download_pdfs:
        return finder.find_and_download_reports(plant_name)
    else:
        webpage_links = finder.get_annual_report_webpage_links(plant_name)
        return {
            'plant_name': plant_name,
            'annual_report_pages': webpage_links,
            'status': 'webpage_links_only'
        }
