"""
Specialized Power Plant Annual Report LangGraph Agent

This agent combines LangGraph research capabilities with Selenium PDF extraction
to find and download power plant annual reports.

Workflow:
1. User provides power plant name
2. LangGraph agent researches and finds annual report webpages
3. Selenium automatically scrapes those pages for PDF links
4. Downloads the PDF files
"""

import os
import re
from typing import Dict, Any, List

from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import RunnableConfig
from langchain_google_genai import ChatGoogleGenerativeAI
from google.genai import Client

from agent.state import OverallState
from agent.configuration import Configuration
from agent.prompts import get_current_date
from agent.power_plant_search import WebPageScraper, PDFDownloader
from agent.utils import get_research_topic

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def detect_power_plant_query(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Extract power plant name from user query and prepare for research.
    
    Args:
        state: Current graph state containing the user's messages
        config: Configuration for the runnable
        
    Returns:
        Dictionary with extracted plant name and research queries
    """
    configurable = Configuration.from_runnable_config(config)
    
    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if msg.type == "human"]
    if not user_messages:
        return {"search_query": ["No query provided"]}
    
    latest_message = user_messages[-1].content
    
    # Extract plant name using LLM
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    prompt = f"""
    Extract the power plant name from the following user message and create search queries 
    to find annual reports for this power plant.
    
    User message: {latest_message}
    
    Create 3-5 specific search queries that would help find:
    1. Direct annual reports for this power plant
    2. The holding/parent company's annual reports
    3. Investor relations pages
    4. SEC filings if it's a US company
    
    Return only a JSON list of search queries, nothing else.
    Example: ["Palo Verde Nuclear Plant annual report", "Arizona Public Service annual report", "APS investor relations"]
    """
    
    try:
        response = llm.invoke(prompt)
        # Try to extract JSON from response
        content = response.content.strip()
        if content.startswith('[') and content.endswith(']'):
            import json
            queries = json.loads(content)
            return {"search_query": queries}
        else:
            # Fallback: create basic queries
            plant_name = latest_message.strip()
            return {"search_query": [
                f'"{plant_name}" annual report',
                f'"{plant_name}" financial statements',
                f'"{plant_name}" investor relations'
            ]}
    except Exception:
        # Fallback: create basic queries
        plant_name = latest_message.strip()
        return {"search_query": [
            f'"{plant_name}" annual report',
            f'"{plant_name}" financial statements',
            f'"{plant_name}" investor relations'
        ]}


def research_annual_reports(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Use LangGraph's research capabilities to find annual report webpages.
    
    Args:
        state: Current graph state containing search queries
        config: Configuration for the runnable
        
    Returns:
        Dictionary with research results and webpage links
    """
    configurable = Configuration.from_runnable_config(config)
    
    all_results = []
    all_sources = []
    
    # Research each query
    for query in state.get("search_query", []):
        formatted_prompt = f"""
        Search for annual reports and financial documents related to: {query}
        
        Focus on finding:
        - Official company annual reports
        - SEC filings and investor relations pages
        - Financial statements and disclosures
        - Corporate governance documents
        
        Current date: {get_current_date()}
        """
        
        try:
            # Use Google Search API through Gemini
            response = genai_client.models.generate_content(
                model=configurable.query_generator_model,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                },
            )
            
            # Extract search results
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    all_results.append(candidate.content.parts[0].text)
                
                # Extract grounding metadata (URLs)
                if hasattr(candidate, 'grounding_metadata') and candidate.grounding_metadata:
                    for chunk in candidate.grounding_metadata.grounding_chunks:
                        if hasattr(chunk, 'web') and chunk.web:
                            all_sources.append({
                                'title': chunk.web.title or 'No title',
                                'link': chunk.web.uri,
                                'snippet': 'Found via Google Search'
                            })
                            
        except Exception as e:
            print(f"Error in research for query '{query}': {e}")
            continue
    
    return {
        "web_research_result": all_results,
        "sources_gathered": all_sources
    }


def extract_annual_report_pages(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Filter and identify the most relevant annual report pages from research results.
    
    Args:
        state: Current graph state with research results
        config: Configuration for the runnable
        
    Returns:
        Dictionary with filtered annual report page URLs
    """
    sources = state.get("sources_gathered", [])
    
    # Keywords that indicate annual report pages (more inclusive)
    annual_report_keywords = [
        'annual report', 'annual reports', 'investor relations', 'financial statements',
        'sec filing', '10-k', 'form 10-k', 'corporate governance', 'financial disclosure',
        'earnings', 'quarterly report', 'sustainability report', 'financial', 'report',
        'pdf', 'document', 'filing', 'disclosure', 'corporate', 'investor'
    ]

    # Filter sources for annual report pages (more inclusive)
    annual_report_pages = []
    for source in sources:
        title = source.get('title', '').lower()
        link = source.get('link', '').lower()
        snippet = source.get('snippet', '').lower()

        # Check if this looks like an annual report page
        combined_text = f"{title} {link} {snippet}"
        if any(keyword in combined_text for keyword in annual_report_keywords):
            annual_report_pages.append(source)
        # Also include PDF links directly
        elif '.pdf' in link or 'pdf' in title:
            annual_report_pages.append(source)
    
    # Remove duplicates based on URL
    seen_urls = set()
    unique_pages = []
    for page in annual_report_pages:
        url = page.get('link', '')
        if url and url not in seen_urls:
            seen_urls.add(url)
            unique_pages.append(page)
    
    return {"annual_report_pages": unique_pages}


def scrape_pdfs_with_selenium(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Use Selenium to scrape the annual report pages and extract PDF links.
    
    Args:
        state: Current graph state with annual report page URLs
        config: Configuration for the runnable
        
    Returns:
        Dictionary with PDF links and download results
    """
    annual_report_pages = state.get("annual_report_pages", [])
    
    if not annual_report_pages:
        return {
            "pdf_links": [],
            "downloaded_files": [],
            "scraping_status": "no_pages_to_scrape"
        }
    
    # Initialize Selenium scraper
    scraper = WebPageScraper(headless=True)
    downloader = PDFDownloader("downloads")
    
    all_pdf_links = []
    downloaded_files = []
    
    try:
        # Extract plant name for organizing downloads
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        plant_name = user_messages[-1].content.strip() if user_messages else "unknown_plant"
        plant_name = re.sub(r'[^\w\s-]', '', plant_name).strip()
        
        # Scrape each annual report page
        for page in annual_report_pages[:5]:  # Limit to top 5 pages to avoid timeout
            page_url = page.get('link', '')
            if not page_url:
                continue
                
            print(f"Scraping: {page_url}")
            
            # Get PDF links from this page
            pdf_links = scraper.scrape_pdf_links_from_page(page_url)
            all_pdf_links.extend(pdf_links)
            
            # Also look for annual report links that might lead to PDFs
            annual_links = scraper.scrape_annual_report_links(page_url)
            for link in annual_links:
                if link.get('type') == 'pdf':
                    all_pdf_links.append(link['url'])
        
        # Remove duplicates
        unique_pdf_links = list(set(all_pdf_links))
        
        # Download PDFs
        if unique_pdf_links:
            downloaded_files = downloader.download_multiple_pdfs(unique_pdf_links, plant_name)
        
        return {
            "pdf_links": unique_pdf_links,
            "downloaded_files": downloaded_files,
            "scraping_status": "completed"
        }
        
    except Exception as e:
        print(f"Error in Selenium scraping: {e}")
        return {
            "pdf_links": [],
            "downloaded_files": [],
            "scraping_status": f"error: {str(e)}"
        }
    finally:
        # Clean up Selenium driver
        scraper.close_driver()


def format_final_response(state: OverallState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Format the final response with all findings.
    
    Args:
        state: Current graph state with all results
        config: Configuration for the runnable
        
    Returns:
        Dictionary with formatted AI response
    """
    # Extract plant name
    user_messages = [msg for msg in state["messages"] if msg.type == "human"]
    plant_name = user_messages[-1].content.strip() if user_messages else "Unknown Plant"
    
    annual_report_pages = state.get("annual_report_pages", [])
    pdf_links = state.get("pdf_links", [])
    downloaded_files = state.get("downloaded_files", [])
    scraping_status = state.get("scraping_status", "unknown")
    
    # Build response
    response_content = f"""# Power Plant Annual Report Search Results

**Power Plant:** {plant_name}
**Search Status:** {scraping_status}

## 📄 Annual Report Webpages Found ({len(annual_report_pages)} pages)

"""
    
    if annual_report_pages:
        for i, page in enumerate(annual_report_pages, 1):
            title = page.get('title', 'No title')
            link = page.get('link', 'No link')
            response_content += f"{i}. **{title}**\n   🔗 {link}\n\n"
    else:
        response_content += "No annual report webpages found.\n\n"
    
    if pdf_links:
        response_content += f"## 📁 PDF Links Extracted ({len(pdf_links)} PDFs)\n\n"
        for i, pdf_link in enumerate(pdf_links, 1):
            response_content += f"{i}. [Download PDF]({pdf_link})\n"
        response_content += "\n"
    
    if downloaded_files:
        response_content += f"## ⬇️ Downloaded Files ({len(downloaded_files)} files)\n\n"
        for i, file_path in enumerate(downloaded_files, 1):
            filename = os.path.basename(file_path)
            response_content += f"{i}. **{filename}**\n   📁 `{file_path}`\n\n"
    
    if not annual_report_pages and not pdf_links:
        response_content += """
## ⚠️ No Results Found

This could be due to:
- The power plant name might need to be more specific
- Annual reports might not be publicly available
- The plant might be owned by a holding company (try searching for the parent company)

**Suggestions:**
- Try the full official name of the power plant
- Search for the operating company or holding company
- Check if the plant has been renamed or sold
"""
    
    # Create AI message
    ai_message = AIMessage(content=response_content)
    
    return {"messages": [ai_message]}


# Create the Power Plant Search Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Add nodes
builder.add_node("detect_power_plant_query", detect_power_plant_query)
builder.add_node("research_annual_reports", research_annual_reports)
builder.add_node("extract_annual_report_pages", extract_annual_report_pages)
builder.add_node("scrape_pdfs_with_selenium", scrape_pdfs_with_selenium)
builder.add_node("format_final_response", format_final_response)

# Set entry point
builder.add_edge(START, "detect_power_plant_query")

# Connect the workflow
builder.add_edge("detect_power_plant_query", "research_annual_reports")
builder.add_edge("research_annual_reports", "extract_annual_report_pages")
builder.add_edge("extract_annual_report_pages", "scrape_pdfs_with_selenium")
builder.add_edge("scrape_pdfs_with_selenium", "format_final_response")
builder.add_edge("format_final_response", END)

# Compile the graph
power_plant_agent = builder.compile(name="power-plant-annual-report-agent")
