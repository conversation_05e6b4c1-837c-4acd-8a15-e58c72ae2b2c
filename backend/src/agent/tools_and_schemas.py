from typing import List, Optional
from pydantic import BaseModel, Field


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


class PowerPlantSearchRequest(BaseModel):
    plant_name: str = Field(
        description="The name of the power plant to search for annual reports."
    )
    download_pdfs: bool = Field(
        default=True,
        description="Whether to download PDF files or just return webpage links."
    )


class PowerPlantSearchResult(BaseModel):
    plant_name: str = Field(
        description="The name of the power plant that was searched."
    )
    holding_company: Optional[str] = Field(
        description="The holding company name if found."
    )
    annual_report_pages: List[dict] = Field(
        description="List of webpage links containing annual reports."
    )
    pdf_links: List[str] = Field(
        description="List of PDF URLs found."
    )
    downloaded_files: List[str] = Field(
        description="List of successfully downloaded file paths."
    )
    status: str = Field(
        description="Status of the search operation."
    )
    error: Optional[str] = Field(
        description="Error message if the search failed."
    )
