#!/usr/bin/env python3
"""
Test script for the Power Plant Search Engine

This script tests the power plant search functionality without requiring
the full LangGraph setup. It's useful for debugging and development.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agent.power_plant_search import PowerPlantReportFinder, search_power_plant_reports
from dotenv import load_dotenv

def test_basic_search():
    """Test basic power plant search functionality."""
    print("🔍 Testing Power Plant Search Engine")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test plant names
    test_plants = [
        "Palo Verde Nuclear Plant",
        "Three Mile Island",
        "Diablo Canyon Power Plant"
    ]
    
    for plant_name in test_plants:
        print(f"\n📍 Testing: {plant_name}")
        print("-" * 30)
        
        try:
            # Test webpage links only (faster for testing)
            result = search_power_plant_reports(
                plant_name=plant_name,
                download_pdfs=False,  # Set to False for faster testing
                google_api_key=os.getenv("GOOGLE_API_KEY"),
                search_engine_id=os.getenv("GOOGLE_SEARCH_ENGINE_ID")
            )
            
            print(f"Status: {result.get('status', 'unknown')}")
            
            if result.get('annual_report_pages'):
                print(f"Found {len(result['annual_report_pages'])} webpage links:")
                for i, page in enumerate(result['annual_report_pages'][:3], 1):
                    print(f"  {i}. {page.get('title', 'No title')}")
                    print(f"     {page.get('link', 'No link')}")
            else:
                print("No annual report pages found")
                
        except Exception as e:
            print(f"❌ Error testing {plant_name}: {e}")
    
    print("\n✅ Test completed!")


def test_with_download():
    """Test with PDF download (requires more time)."""
    print("🔍 Testing Power Plant Search with PDF Download")
    print("=" * 50)
    
    load_dotenv()
    
    plant_name = "Palo Verde Nuclear Plant"  # Well-known plant for testing
    
    print(f"\n📍 Testing with downloads: {plant_name}")
    print("-" * 40)
    
    try:
        result = search_power_plant_reports(
            plant_name=plant_name,
            download_pdfs=True,
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            search_engine_id=os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        )
        
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Plant: {result.get('plant_name', 'Unknown')}")
        print(f"Holding Company: {result.get('holding_company', 'Not found')}")
        
        if result.get('annual_report_pages'):
            print(f"\nWebpage Links ({len(result['annual_report_pages'])}):")
            for i, page in enumerate(result['annual_report_pages'][:3], 1):
                print(f"  {i}. {page.get('title', 'No title')}")
        
        if result.get('pdf_links'):
            print(f"\nPDF Links ({len(result['pdf_links'])}):")
            for i, pdf_link in enumerate(result['pdf_links'][:5], 1):
                print(f"  {i}. {pdf_link}")
        
        if result.get('downloaded_files'):
            print(f"\nDownloaded Files ({len(result['downloaded_files'])}):")
            for i, file_path in enumerate(result['downloaded_files'], 1):
                print(f"  {i}. {file_path}")
        
        if result.get('error'):
            print(f"\n❌ Error: {result['error']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n✅ Test with download completed!")


def test_individual_components():
    """Test individual components of the search engine."""
    print("🔧 Testing Individual Components")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        finder = PowerPlantReportFinder(
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            search_engine_id=os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        )
        
        plant_name = "Palo Verde Nuclear Plant"
        
        # Test 1: Direct power plant search
        print(f"\n1️⃣ Testing direct search for {plant_name}")
        direct_results = finder.search_engine.search_power_plant_reports(plant_name)
        print(f"   Found {len(direct_results)} direct results")
        
        # Test 2: Holding company search
        print(f"\n2️⃣ Testing holding company search for {plant_name}")
        holding_company = finder.search_engine.search_holding_company(plant_name)
        print(f"   Holding company: {holding_company or 'Not found'}")
        
        # Test 3: Company annual reports search
        if holding_company:
            print(f"\n3️⃣ Testing company reports search for {holding_company}")
            company_results = finder.search_engine.search_company_annual_reports(holding_company)
            print(f"   Found {len(company_results)} company results")
            
            # Test 4: Filter investor relations pages
            print(f"\n4️⃣ Testing investor relations filter")
            ir_pages = finder.search_engine.filter_investor_relations_pages(company_results)
            print(f"   Found {len(ir_pages)} investor relations pages")
        
        print("\n✅ Component tests completed!")
        
    except Exception as e:
        print(f"❌ Error in component tests: {e}")


def main():
    """Main test function."""
    print("🚀 Power Plant Search Engine Test Suite")
    print("=" * 60)
    
    # Check for required environment variables
    load_dotenv()
    
    if not os.getenv("GOOGLE_API_KEY") or not os.getenv("GOOGLE_SEARCH_ENGINE_ID"):
        print("⚠️  Warning: Google API credentials not found in environment")
        print("   The search engine will use fallback methods")
        print("   Set GOOGLE_API_KEY and GOOGLE_SEARCH_ENGINE_ID for full functionality")
    
    print("\nChoose a test to run:")
    print("1. Basic search (webpage links only)")
    print("2. Full search with PDF download")
    print("3. Test individual components")
    print("4. Run all tests")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        test_basic_search()
    elif choice == "2":
        test_with_download()
    elif choice == "3":
        test_individual_components()
    elif choice == "4":
        test_basic_search()
        test_individual_components()
        print("\n" + "=" * 60)
        print("Note: Skipping download test in 'run all' mode")
        print("Run option 2 separately to test downloads")
    else:
        print("Invalid choice. Please run the script again.")


if __name__ == "__main__":
    main()
