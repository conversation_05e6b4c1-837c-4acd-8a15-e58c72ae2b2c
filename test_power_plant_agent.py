#!/usr/bin/env python3
"""
Test script for the specialized Power Plant LangGraph Agent

This script tests the new agent that combines LangGraph research with Selenium PDF extraction.
"""

import os
import sys
from pathlib import Path

# Add the backend src directory to Python path
backend_src = Path(__file__).parent / "backend" / "src"
if backend_src.exists():
    sys.path.insert(0, str(backend_src))

from langchain_core.messages import HumanMessage
from agent.power_plant_agent import power_plant_agent
from dotenv import load_dotenv

def test_power_plant_agent():
    """Test the specialized power plant agent."""
    print("🔍 Testing Specialized Power Plant LangGraph Agent")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Check required API keys
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY not found in .env file")
        return
    
    print("✅ GEMINI_API_KEY found")
    print("✅ SCRAPER_API_KEY found" if os.getenv("SCRAPER_API_KEY") else "⚠️  SCRAPER_API_KEY not found")
    print()
    
    # Test plant names
    test_plants = [
        "PLTU Suparma",
        "Palo Verde Nuclear Plant",
        "Three Mile Island"
    ]
    
    for plant_name in test_plants:
        print(f"\n🏭 Testing: {plant_name}")
        print("-" * 40)
        
        try:
            # Create initial state
            initial_state = {
                "messages": [HumanMessage(content=plant_name)],
                "search_query": [],
                "web_research_result": [],
                "sources_gathered": [],
                "initial_search_query_count": 3,
                "max_research_loops": 1,
                "research_loop_count": 0,
                "reasoning_model": "gemini-2.0-flash"
            }
            
            print("🚀 Starting LangGraph agent...")
            
            # Run the agent
            result = power_plant_agent.invoke(initial_state)
            
            # Extract results
            final_messages = result.get("messages", [])
            annual_report_pages = result.get("annual_report_pages", [])
            pdf_links = result.get("pdf_links", [])
            downloaded_files = result.get("downloaded_files", [])
            scraping_status = result.get("scraping_status", "unknown")
            
            print(f"✅ Agent completed!")
            print(f"📄 Found {len(annual_report_pages)} annual report pages")
            print(f"📁 Extracted {len(pdf_links)} PDF links")
            print(f"⬇️  Downloaded {len(downloaded_files)} files")
            print(f"🔧 Scraping status: {scraping_status}")
            
            # Show the final AI response
            if final_messages:
                last_message = final_messages[-1]
                if hasattr(last_message, 'content'):
                    print("\n📝 Agent Response:")
                    print("-" * 30)
                    print(last_message.content[:500] + "..." if len(last_message.content) > 500 else last_message.content)
            
            print("\n" + "=" * 60)
            
        except Exception as e:
            print(f"❌ Error testing {plant_name}: {e}")
            import traceback
            traceback.print_exc()
            print("\n" + "=" * 60)


def test_individual_nodes():
    """Test individual nodes of the agent."""
    print("🔧 Testing Individual Agent Nodes")
    print("=" * 60)
    
    load_dotenv()
    
    try:
        from agent.power_plant_agent import (
            detect_power_plant_query, 
            research_annual_reports,
            extract_annual_report_pages,
            scrape_pdfs_with_selenium,
            format_final_response
        )
        from agent.configuration import Configuration
        
        plant_name = "PLTU Suparma"
        
        # Test 1: Query detection
        print(f"\n1️⃣ Testing query detection for: {plant_name}")
        initial_state = {
            "messages": [HumanMessage(content=plant_name)],
            "search_query": []
        }
        config = Configuration()
        
        result1 = detect_power_plant_query(initial_state, config)
        search_queries = result1.get("search_query", [])
        print(f"   Generated {len(search_queries)} search queries:")
        for i, query in enumerate(search_queries, 1):
            print(f"   {i}. {query}")
        
        # Test 2: Research (limited test)
        print(f"\n2️⃣ Testing research phase...")
        state2 = {**initial_state, **result1}
        result2 = research_annual_reports(state2, config)
        sources = result2.get("sources_gathered", [])
        print(f"   Found {len(sources)} sources from research")
        
        # Test 3: Extract annual report pages
        print(f"\n3️⃣ Testing annual report page extraction...")
        state3 = {**state2, **result2}
        result3 = extract_annual_report_pages(state3, config)
        pages = result3.get("annual_report_pages", [])
        print(f"   Filtered to {len(pages)} annual report pages")
        
        print("\n✅ Individual node tests completed!")
        
    except Exception as e:
        print(f"❌ Error in individual node tests: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main test function."""
    print("🚀 Power Plant LangGraph Agent Test Suite")
    print("=" * 70)
    
    print("\nChoose a test to run:")
    print("1. Test full agent workflow")
    print("2. Test individual nodes")
    print("3. Run both tests")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        test_power_plant_agent()
    elif choice == "2":
        test_individual_nodes()
    elif choice == "3":
        test_individual_nodes()
        print("\n" + "=" * 70)
        test_power_plant_agent()
    else:
        print("Invalid choice. Please run the script again.")


if __name__ == "__main__":
    main()
