#!/usr/bin/env python3
"""
Quick test to verify the agent is working step by step
"""

import os
import sys
from pathlib import Path

# Add the backend src directory to Python path
backend_src = Path(__file__).parent / "backend" / "src"
if backend_src.exists():
    sys.path.insert(0, str(backend_src))

from langchain_core.messages import HumanMessage
from agent.power_plant_agent import (
    detect_power_plant_query,
    research_annual_reports, 
    extract_annual_report_pages,
    scrape_pdfs_with_selenium,
    format_final_response
)
from agent.configuration import Configuration
from dotenv import load_dotenv

def test_step_by_step(plant_name=None):
    """Test each step individually to find the issue."""
    load_dotenv()

    if not plant_name:
        plant_name = input("Enter power plant name: ").strip()
        if not plant_name:
            plant_name = "PLTU Suparma"  # Default fallback

    print(f"🔍 Testing step-by-step for: {plant_name}")
    print("=" * 50)
    
    # Initial state
    state = {
        "messages": [HumanMessage(content=plant_name)],
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_pages": [],
        "pdf_links": [],
        "downloaded_files": [],
        "scraping_status": "unknown"
    }
    
    config = Configuration()
    
    try:
        # Step 1: Query detection
        print("1️⃣ Query Detection...")
        result1 = detect_power_plant_query(state, config)
        state.update(result1)
        print(f"   Generated {len(state['search_query'])} queries")
        
        # Step 2: Research
        print("2️⃣ Research...")
        result2 = research_annual_reports(state, config)
        state.update(result2)
        print(f"   Found {len(state['sources_gathered'])} sources")
        
        # Step 3: Extract pages
        print("3️⃣ Extract Annual Report Pages...")
        result3 = extract_annual_report_pages(state, config)
        state.update(result3)
        print(f"   Filtered to {len(state['annual_report_pages'])} pages")
        
        # Step 4: Show what we have so far
        print("4️⃣ Current State Check...")
        print(f"   Annual report pages in state: {len(state.get('annual_report_pages', []))}")

        if state.get('annual_report_pages'):
            print("   📄 Annual Report Webpages Found:")
            for i, page in enumerate(state['annual_report_pages'][:3], 1):  # Show first 3
                title = page.get('title', 'No title')
                link = page.get('link', 'No link')
                print(f"   {i}. {title[:60]}...")
                print(f"      🔗 {link}")
            if len(state['annual_report_pages']) > 3:
                print(f"   ... and {len(state['annual_report_pages']) - 3} more pages")
        
        # Step 5: Selenium scraping (simplified)
        print("5️⃣ Selenium Scraping...")
        result4 = scrape_pdfs_with_selenium(state, config)
        state.update(result4)

        pdf_links = state.get('pdf_links', [])
        downloaded_files = state.get('downloaded_files', [])

        print(f"   Found {len(pdf_links)} PDF links")
        print(f"   Downloaded {len(downloaded_files)} files")

        # Show PDF links
        if pdf_links:
            print("   📁 PDF Links Found:")
            for i, pdf_link in enumerate(pdf_links[:5], 1):  # Show first 5
                print(f"   {i}. {pdf_link}")
            if len(pdf_links) > 5:
                print(f"   ... and {len(pdf_links) - 5} more PDF links")

        # Show downloaded files
        if downloaded_files:
            print("   ⬇️ Downloaded Files:")
            for i, file_path in enumerate(downloaded_files[:5], 1):  # Show first 5
                filename = file_path.split('/')[-1]  # Get just the filename
                print(f"   {i}. {filename}")
            if len(downloaded_files) > 5:
                print(f"   ... and {len(downloaded_files) - 5} more files")

            print(f"\n📂 All files saved in: downloads/{plant_name.replace(' ', '_')}/")

        # Step 6: Final response
        print("6️⃣ Final Response...")
        result5 = format_final_response(state, config)
        state.update(result5)

        print("\n✅ Step-by-step test completed!")
        print(f"📊 Final Results:")
        print(f"   📄 Webpages: {len(state.get('annual_report_pages', []))}")
        print(f"   📁 PDF Links: {len(state.get('pdf_links', []))}")
        print(f"   ⬇️ Downloads: {len(state.get('downloaded_files', []))}")

        return state
        
    except Exception as e:
        print(f"❌ Error in step-by-step test: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_step_by_step()
