#!/usr/bin/env python3
"""
Quick test to verify the agent is working step by step
"""

import os
import sys
from pathlib import Path

# Add the backend src directory to Python path
backend_src = Path(__file__).parent / "backend" / "src"
if backend_src.exists():
    sys.path.insert(0, str(backend_src))

from langchain_core.messages import HumanMessage
from agent.power_plant_agent import (
    detect_power_plant_query,
    research_annual_reports, 
    extract_annual_report_pages,
    scrape_pdfs_with_selenium,
    format_final_response
)
from agent.configuration import Configuration
from dotenv import load_dotenv

def test_step_by_step():
    """Test each step individually to find the issue."""
    load_dotenv()
    
    plant_name = "PLTU Suparma"
    print(f"🔍 Testing step-by-step for: {plant_name}")
    print("=" * 50)
    
    # Initial state
    state = {
        "messages": [HumanMessage(content=plant_name)],
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_pages": [],
        "pdf_links": [],
        "downloaded_files": [],
        "scraping_status": "unknown"
    }
    
    config = Configuration()
    
    try:
        # Step 1: Query detection
        print("1️⃣ Query Detection...")
        result1 = detect_power_plant_query(state, config)
        state.update(result1)
        print(f"   Generated {len(state['search_query'])} queries")
        
        # Step 2: Research
        print("2️⃣ Research...")
        result2 = research_annual_reports(state, config)
        state.update(result2)
        print(f"   Found {len(state['sources_gathered'])} sources")
        
        # Step 3: Extract pages
        print("3️⃣ Extract Annual Report Pages...")
        result3 = extract_annual_report_pages(state, config)
        state.update(result3)
        print(f"   Filtered to {len(state['annual_report_pages'])} pages")
        
        # Step 4: Show what we have so far
        print("4️⃣ Current State Check...")
        print(f"   Annual report pages in state: {len(state.get('annual_report_pages', []))}")
        
        if state.get('annual_report_pages'):
            print("   First page:")
            first_page = state['annual_report_pages'][0]
            print(f"   Title: {first_page.get('title', 'No title')[:60]}...")
            print(f"   Link: {first_page.get('link', 'No link')[:80]}...")
        
        # Step 5: Selenium scraping (simplified)
        print("5️⃣ Selenium Scraping...")
        result4 = scrape_pdfs_with_selenium(state, config)
        state.update(result4)
        print(f"   Found {len(state.get('pdf_links', []))} PDF links")
        print(f"   Downloaded {len(state.get('downloaded_files', []))} files")
        
        # Step 6: Final response
        print("6️⃣ Final Response...")
        result5 = format_final_response(state, config)
        state.update(result5)
        
        print("\n✅ Step-by-step test completed!")
        print(f"Final state: {len(state.get('annual_report_pages', []))} pages, {len(state.get('pdf_links', []))} PDFs")
        
        return state
        
    except Exception as e:
        print(f"❌ Error in step-by-step test: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_step_by_step()
