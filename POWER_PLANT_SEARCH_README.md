# Power Plant Annual Report Search Engine

A comprehensive search engine designed specifically for finding power plant annual reports and financial documents. This system uses advanced web scraping, intelligent search algorithms, and automated PDF downloading to locate and retrieve annual reports for any power plant.

## 🚀 Features

### Core Functionality
- **Smart Power Plant Search**: Automatically searches for annual reports using the power plant name
- **Holding Company Detection**: If direct reports aren't found, identifies and searches the parent/holding company
- **Multi-Source Search**: Searches multiple sources including company websites, SEC filings, and investor relations pages
- **Intelligent Filtering**: Automatically filters results to focus on annual reports and financial documents
- **PDF Auto-Download**: Uses Selenium to parse web pages and automatically download PDF reports
- **Webpage Link Extraction**: Provides links to pages containing multiple annual reports

### Search Strategy
1. **Direct Search**: Searches for "{power plant name} annual report"
2. **Holding Company Search**: Finds the parent company using queries like "{power plant name} holding company"
3. **Company Website Search**: Searches the holding company's investor relations and financial pages
4. **PDF Extraction**: Uses Selenium to scrape web pages and find PDF download links
5. **Automated Download**: Downloads all found PDF reports to organized folders

## 🛠️ Installation

### Prerequisites
- Python 3.11+
- Chrome browser (for Selenium)
- Google Custom Search API credentials (optional but recommended)

### Backend Setup

1. **Install Dependencies**:
   ```bash
   cd backend
   pip install -e .
   ```

2. **Environment Configuration**:
   Copy `.env.example` to `.env` and configure:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your API keys:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   GOOGLE_API_KEY=your_google_api_key_here
   GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
   ```

3. **Chrome Driver Setup**:
   The system uses Selenium with Chrome. Ensure Chrome is installed and accessible.

### Frontend Setup

1. **Install Dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Build Frontend**:
   ```bash
   npm run build
   ```

## 🔧 API Configuration

### Google Custom Search API (Recommended)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Custom Search API
3. Create credentials and get your API key
4. Set up a Custom Search Engine at [Google CSE](https://cse.google.com/)
5. Configure to search the entire web
6. Get your Search Engine ID

### Alternative: Fallback Search
If Google API is not configured, the system will use fallback search methods with reduced functionality.

## 🚀 Usage

### Web Interface

1. **Start the Backend**:
   ```bash
   cd backend
   langgraph up
   ```

2. **Access the Interface**:
   Open your browser to `http://localhost:8123/app`

3. **Switch to Power Plant Mode**:
   Click the "Power Plant Search" button in the top-right corner

4. **Search for Reports**:
   - Enter a power plant name (e.g., "Palo Verde Nuclear Plant")
   - Click "Search"
   - Wait for results and downloads

### Command Line Testing

Run the test script to verify functionality:

```bash
cd backend
python test_power_plant_search.py
```

### Programmatic Usage

```python
from agent.power_plant_search import search_power_plant_reports

# Search with PDF download
result = search_power_plant_reports(
    plant_name="Palo Verde Nuclear Plant",
    download_pdfs=True
)

# Search for webpage links only
result = search_power_plant_reports(
    plant_name="Three Mile Island",
    download_pdfs=False
)

print(f"Status: {result['status']}")
print(f"Found {len(result['pdf_links'])} PDF links")
print(f"Downloaded {len(result['downloaded_files'])} files")
```

## 📁 Output Structure

Downloaded files are organized as follows:
```
downloads/
├── Palo_Verde_Nuclear_Plant/
│   ├── Palo_Verde_Nuclear_Plant_report_1.pdf
│   ├── Palo_Verde_Nuclear_Plant_report_2.pdf
│   └── ...
├── Three_Mile_Island/
│   ├── Three_Mile_Island_report_1.pdf
│   └── ...
```

## 🔍 Search Examples

### Successful Searches
- "Palo Verde Nuclear Plant"
- "Three Mile Island"
- "Diablo Canyon Power Plant"
- "Indian Point Energy Center"
- "Vogtle Electric Generating Plant"

### Search Tips
- Use the full, official name of the power plant
- Include "Nuclear Plant", "Power Plant", or "Generating Station" in the name
- If no results are found, try alternative names or abbreviations
- Check the holding company name if direct search fails

## 🛠️ Architecture

### Components

1. **PowerPlantSearchEngine**: Core search logic and Google API integration
2. **WebPageScraper**: Selenium-based web scraping for PDF extraction
3. **PDFDownloader**: Handles PDF file downloads and organization
4. **PowerPlantReportFinder**: Main orchestrator class
5. **LangGraph Integration**: Integrates with the LangGraph agent framework

### Search Flow

```mermaid
graph TD
    A[User Input: Plant Name] --> B[Direct Plant Search]
    B --> C{Found Reports?}
    C -->|Yes| D[Extract PDF Links]
    C -->|No| E[Search Holding Company]
    E --> F[Company Reports Search]
    F --> G[Filter IR Pages]
    G --> H[Scrape Web Pages]
    H --> D
    D --> I[Download PDFs]
    I --> J[Return Results]
```

## 🔧 Configuration Options

### Search Engine Configuration
- `google_api_key`: Google Custom Search API key
- `search_engine_id`: Google Custom Search Engine ID
- `download_dir`: Directory for downloaded files (default: "downloads")

### Scraper Configuration
- `headless`: Run browser in headless mode (default: True)
- `timeout`: Page load timeout in seconds (default: 30)

### Download Configuration
- `max_files`: Maximum number of files to download per plant
- `file_size_limit`: Maximum file size for downloads

## 🐛 Troubleshooting

### Common Issues

1. **Chrome Driver Issues**:
   - Ensure Chrome browser is installed
   - Update Chrome to the latest version
   - Check that ChromeDriver is compatible

2. **API Rate Limits**:
   - Google Custom Search has daily limits
   - Add delays between requests if needed
   - Consider using multiple API keys for high volume

3. **No Results Found**:
   - Verify the power plant name spelling
   - Try alternative names or abbreviations
   - Check if the plant has been renamed or sold

4. **Download Failures**:
   - Some PDFs may be behind authentication
   - Corporate firewalls may block access
   - File size limits may prevent downloads

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script to verify setup
3. Check the logs for error details
4. Open an issue with detailed information

## 🔮 Future Enhancements

- Support for international power plants
- Integration with SEC EDGAR database
- OCR for scanned PDF documents
- Historical report tracking
- Automated report analysis
- Email notifications for new reports
