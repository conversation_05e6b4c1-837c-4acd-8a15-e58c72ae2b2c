# Power Plant Annual Report Search Engine

A specialized LangGraph agent that combines AI-powered research with Selenium web scraping to find and download power plant annual reports. This system uses Google Gemini for intelligent research and Selenium for precise PDF extraction from discovered webpages.

## 🚀 Features

### Core Functionality
- **Smart Power Plant Search**: Automatically searches for annual reports using the power plant name
- **Holding Company Detection**: If direct reports aren't found, identifies and searches the parent/holding company
- **Multi-Source Search**: Searches multiple sources including company websites, SEC filings, and investor relations pages
- **Intelligent Filtering**: Automatically filters results to focus on annual reports and financial documents
- **PDF Auto-Download**: Uses Selenium to parse web pages and automatically download PDF reports
- **Webpage Link Extraction**: Provides links to pages containing multiple annual reports

### Two-Stage Search Strategy
**Stage 1: LangGraph AI Research**
1. **Query Generation**: AI extracts plant name and creates targeted search queries
2. **Intelligent Research**: Uses Google Gemini with Google Search API to find annual report webpages
3. **Source Filtering**: AI identifies the most relevant annual report and investor relations pages

**Stage 2: Selenium PDF Extraction**
4. **Web Scraping**: Selenium scrapes the discovered webpages for PDF download links
5. **PDF Extraction**: Finds direct links to annual report PDFs
6. **Automated Download**: Downloads all found PDF reports to organized folders

## 🛠️ Installation

### Prerequisites
- Python 3.11+
- Chrome browser (for Selenium)
- Google Gemini API key (required for LangGraph agent)
- ScraperAPI key (optional, for enhanced search)

### Quick Setup

1. **Install Dependencies**:
   ```bash
   cd backend
   pip install -e .
   ```

2. **Environment Configuration**:
   Copy `backend/.env.example` to `backend/.env` and configure:
   ```bash
   cp backend/.env.example backend/.env
   ```

   Edit `backend/.env` with your API keys:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   SCRAPER_API_KEY=your_scraper_api_key_here
   ```

3. **Chrome Driver Setup**:
   The system uses Selenium with Chrome. Ensure Chrome is installed and accessible.

## 🔧 API Configuration

### Google Custom Search API (Recommended)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Custom Search API
3. Create credentials and get your API key
4. Set up a Custom Search Engine at [Google CSE](https://cse.google.com/)
5. Configure to search the entire web
6. Get your Search Engine ID

### Alternative: Fallback Search
If Google API is not configured, the system will use fallback search methods with reduced functionality.

## 🚀 Usage

### LangGraph Agent (Recommended)

1. **Start the LangGraph Server**:
   ```bash
   cd backend
   langgraph up
   ```

2. **Use the API Script**:
   ```bash
   python run_power_plant_agent.py "PLTU Suparma"
   ```

3. **Interactive Mode**:
   ```bash
   python run_power_plant_agent.py
   # Then enter plant name when prompted
   ```

4. **Direct API Call**:
   ```bash
   curl -X POST http://localhost:2024/threads \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"type": "human", "content": "PLTU Suparma"}]}'
   ```

### Testing the Agent

1. **Test the full workflow**:
   ```bash
   python test_power_plant_agent.py
   ```

2. **Test individual components**:
   ```bash
   cd backend
   python test_power_plant_search.py
   ```

### Command Line Interface (Standalone - Legacy)

For direct usage without LangGraph:

```bash
python power_plant_search_standalone.py "PLTU Suparma" --no-download
```

## 📁 Output Structure

Downloaded files are organized as follows:
```
downloads/
├── Palo_Verde_Nuclear_Plant/
│   ├── Palo_Verde_Nuclear_Plant_report_1.pdf
│   ├── Palo_Verde_Nuclear_Plant_report_2.pdf
│   └── ...
├── Three_Mile_Island/
│   ├── Three_Mile_Island_report_1.pdf
│   └── ...
```

## 🔍 Search Examples

### Successful Searches
- "Palo Verde Nuclear Plant"
- "Three Mile Island"
- "Diablo Canyon Power Plant"
- "Indian Point Energy Center"
- "Vogtle Electric Generating Plant"

### Search Tips
- Use the full, official name of the power plant
- Include "Nuclear Plant", "Power Plant", or "Generating Station" in the name
- If no results are found, try alternative names or abbreviations
- Check the holding company name if direct search fails

## 🛠️ Architecture

### Components

1. **PowerPlantSearchEngine**: Core search logic and Google API integration
2. **WebPageScraper**: Selenium-based web scraping for PDF extraction
3. **PDFDownloader**: Handles PDF file downloads and organization
4. **PowerPlantReportFinder**: Main orchestrator class

### Search Flow

```mermaid
graph TD
    A[User Input: Plant Name] --> B[Direct Plant Search]
    B --> C{Found Reports?}
    C -->|Yes| D[Extract PDF Links]
    C -->|No| E[Search Holding Company]
    E --> F[Company Reports Search]
    F --> G[Filter IR Pages]
    G --> H[Scrape Web Pages]
    H --> D
    D --> I[Download PDFs]
    I --> J[Return Results]
```

## 🔧 Configuration Options

### Search Engine Configuration
- `google_api_key`: Google Custom Search API key
- `search_engine_id`: Google Custom Search Engine ID
- `download_dir`: Directory for downloaded files (default: "downloads")

### Scraper Configuration
- `headless`: Run browser in headless mode (default: True)
- `timeout`: Page load timeout in seconds (default: 30)

### Download Configuration
- `max_files`: Maximum number of files to download per plant
- `file_size_limit`: Maximum file size for downloads

## 🐛 Troubleshooting

### Common Issues

1. **Chrome Driver Issues**:
   - Ensure Chrome browser is installed
   - Update Chrome to the latest version
   - Check that ChromeDriver is compatible

2. **API Rate Limits**:
   - Google Custom Search has daily limits
   - Add delays between requests if needed
   - Consider using multiple API keys for high volume

3. **No Results Found**:
   - Verify the power plant name spelling
   - Try alternative names or abbreviations
   - Check if the plant has been renamed or sold

4. **Download Failures**:
   - Some PDFs may be behind authentication
   - Corporate firewalls may block access
   - File size limits may prevent downloads

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script to verify setup
3. Check the logs for error details
4. Open an issue with detailed information

## 🔮 Future Enhancements

- Support for international power plants
- Integration with SEC EDGAR database
- OCR for scanned PDF documents
- Historical report tracking
- Automated report analysis
- Email notifications for new reports
