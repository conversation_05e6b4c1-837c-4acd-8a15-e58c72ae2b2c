import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Search, FileText, Download, Building2 } from "lucide-react";

interface PowerPlantWelcomeScreenProps {
  handleSubmit: (
    submittedInputValue: string,
    effort: string,
    model: string
  ) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const PowerPlantWelcomeScreen: React.FC<PowerPlantWelcomeScreenProps> = ({
  handleSubmit,
  onCancel,
  isLoading,
}) => {
  const [plantName, setPlantName] = useState("");

  const handlePowerPlantSubmit = () => {
    if (plantName.trim()) {
      const query = `Find annual reports for ${plantName.trim()} power plant`;
      handleSubmit(query, "medium", "gemini-2.0-flash");
    }
  };

  const handleExampleClick = (examplePlant: string) => {
    setPlantName(examplePlant);
  };

  const examples = [
    "Palo Verde Nuclear Plant",
    "Three Mile Island",
    "Diablo Canyon Power Plant",
    "Indian Point Energy Center",
    "Vogtle Electric Generating Plant"
  ];

  return (
    <div className="h-full flex flex-col items-center justify-center text-center px-4 flex-1 w-full max-w-4xl mx-auto gap-6">
      <div>
        <div className="flex items-center justify-center gap-3 mb-4">
          <Building2 className="h-12 w-12 text-blue-400" />
          <h1 className="text-4xl md:text-5xl font-semibold text-neutral-100">
            Power Plant Report Finder
          </h1>
        </div>
        <p className="text-lg md:text-xl text-neutral-400 mb-6">
          Find annual reports and financial statements for any power plant
        </p>
      </div>

      <Card className="w-full max-w-2xl bg-neutral-800 border-neutral-700">
        <CardHeader>
          <CardTitle className="text-neutral-100 flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search for Power Plant Reports
          </CardTitle>
          <CardDescription className="text-neutral-400">
            Enter the name of a power plant to find its annual reports, financial statements, and investor relations documents.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="e.g., Palo Verde Nuclear Plant"
              value={plantName}
              onChange={(e) => setPlantName(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handlePowerPlantSubmit()}
              className="flex-1 bg-neutral-700 border-neutral-600 text-neutral-100 placeholder-neutral-400"
              disabled={isLoading}
            />
            <Button
              onClick={handlePowerPlantSubmit}
              disabled={!plantName.trim() || isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isLoading ? "Searching..." : "Search"}
            </Button>
          </div>
          
          <div className="text-left">
            <p className="text-sm text-neutral-400 mb-2">Try these examples:</p>
            <div className="flex flex-wrap gap-2">
              {examples.map((example, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleExampleClick(example)}
                  className="text-xs bg-neutral-700 border-neutral-600 text-neutral-300 hover:bg-neutral-600"
                  disabled={isLoading}
                >
                  {example}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-4xl">
        <Card className="bg-neutral-800 border-neutral-700">
          <CardContent className="p-4 text-center">
            <Search className="h-8 w-8 text-blue-400 mx-auto mb-2" />
            <h3 className="text-sm font-semibold text-neutral-100 mb-1">Smart Search</h3>
            <p className="text-xs text-neutral-400">
              Automatically finds holding companies and searches multiple sources
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-neutral-800 border-neutral-700">
          <CardContent className="p-4 text-center">
            <FileText className="h-8 w-8 text-green-400 mx-auto mb-2" />
            <h3 className="text-sm font-semibold text-neutral-100 mb-1">Multiple Formats</h3>
            <p className="text-xs text-neutral-400">
              Finds both webpage links and direct PDF downloads
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-neutral-800 border-neutral-700">
          <CardContent className="p-4 text-center">
            <Download className="h-8 w-8 text-purple-400 mx-auto mb-2" />
            <h3 className="text-sm font-semibold text-neutral-100 mb-1">Auto Download</h3>
            <p className="text-xs text-neutral-400">
              Automatically downloads available annual report PDFs
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="text-center space-y-2">
        <p className="text-xs text-neutral-500">
          Powered by Google Gemini, LangGraph, and advanced web scraping
        </p>
        <p className="text-xs text-neutral-600">
          This tool searches for publicly available annual reports and financial documents
        </p>
      </div>
    </div>
  );
};
