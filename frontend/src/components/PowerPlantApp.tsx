import { useStream } from "@langchain/langgraph-sdk/react";
import type { Message } from "@langchain/langgraph-sdk";
import { useState, useEffect, useRef, useCallback } from "react";
import { ProcessedEvent } from "@/components/ActivityTimeline";
import { PowerPlantWelcomeScreen } from "@/components/PowerPlantWelcomeScreen";
import { ChatMessagesView } from "@/components/ChatMessagesView";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Building2, Search } from "lucide-react";

export default function PowerPlantApp() {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<
    ProcessedEvent[]
  >([]);
  const [historicalActivities, setHistoricalActivities] = useState<
    Record<string, ProcessedEvent[]>
  >({});
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const hasFinalizeEventOccurredRef = useRef(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("power-plant");

  // Power Plant Search Thread
  const powerPlantThread = useStream<{
    messages: Message[];
    power_plant_search_request: any;
    power_plant_search_result: any;
  }>({
    apiUrl: import.meta.env.DEV
      ? "http://localhost:2024"
      : "http://localhost:8123",
    assistantId: "power-plant-agent",
    messagesKey: "messages",
    onUpdateEvent: (event: any) => {
      let processedEvent: ProcessedEvent | null = null;

      if (event.detect_power_plant_query) {
        processedEvent = {
          title: "Analyzing Query",
          data: "Detecting if this is a power plant search request...",
        };
      } else if (event.power_plant_search) {
        processedEvent = {
          title: "Searching for Power Plant Reports",
          data: "Searching for annual reports and financial documents...",
        };
      } else if (event.format_power_plant_response) {
        processedEvent = {
          title: "Formatting Results",
          data: "Preparing search results and download links...",
        };
        hasFinalizeEventOccurredRef.current = true;
      }

      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
    onError: (error: any) => {
      setError(error.message);
    },
  });

  // General Research Thread
  const generalThread = useStream<{
    messages: Message[];
    initial_search_query_count: number;
    max_research_loops: number;
    reasoning_model: string;
  }>({
    apiUrl: import.meta.env.DEV
      ? "http://localhost:2024"
      : "http://localhost:8123",
    assistantId: "agent",
    messagesKey: "messages",
    onUpdateEvent: (event: any) => {
      let processedEvent: ProcessedEvent | null = null;

      if (event.generate_query) {
        processedEvent = {
          title: "Generating Queries",
          data: "Creating optimized search queries for your research topic.",
        };
      } else if (event.web_research) {
        processedEvent = {
          title: "Web Research",
          data: "Searching the web and gathering information from reliable sources.",
        };
      } else if (event.reflection) {
        processedEvent = {
          title: "Analyzing Results",
          data: "Evaluating gathered information and identifying knowledge gaps.",
        };
      } else if (event.finalize_answer) {
        processedEvent = {
          title: "Finalizing Answer",
          data: "Composing and presenting the final answer.",
        };
        hasFinalizeEventOccurredRef.current = true;
      }

      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
    onError: (error: any) => {
      setError(error.message);
    },
  });

  // Get current thread based on active tab
  const currentThread = activeTab === "power-plant" ? powerPlantThread : generalThread;

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollViewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollViewport) {
        scrollViewport.scrollTop = scrollViewport.scrollHeight;
      }
    }
  }, [currentThread.messages]);

  useEffect(() => {
    if (
      hasFinalizeEventOccurredRef.current &&
      !currentThread.isLoading &&
      currentThread.messages.length > 0
    ) {
      const lastMessage = currentThread.messages[currentThread.messages.length - 1];
      if (lastMessage && lastMessage.type === "ai" && lastMessage.id) {
        setHistoricalActivities((prev) => ({
          ...prev,
          [lastMessage.id!]: [...processedEventsTimeline],
        }));
      }
      hasFinalizeEventOccurredRef.current = false;
    }
  }, [currentThread.messages, currentThread.isLoading, processedEventsTimeline]);

  const handleSubmit = useCallback(
    (submittedInputValue: string, effort: string, model: string) => {
      if (!submittedInputValue.trim()) return;
      setProcessedEventsTimeline([]);
      hasFinalizeEventOccurredRef.current = false;

      const newMessages: Message[] = [
        ...(currentThread.messages || []),
        {
          type: "human",
          content: submittedInputValue,
          id: Date.now().toString(),
        },
      ];

      if (activeTab === "power-plant") {
        powerPlantThread.submit({
          messages: newMessages,
        });
      } else {
        // Convert effort to search parameters for general research
        let initial_search_query_count = 0;
        let max_research_loops = 0;
        switch (effort) {
          case "low":
            initial_search_query_count = 1;
            max_research_loops = 1;
            break;
          case "medium":
            initial_search_query_count = 3;
            max_research_loops = 3;
            break;
          case "high":
            initial_search_query_count = 5;
            max_research_loops = 10;
            break;
        }

        generalThread.submit({
          messages: newMessages,
          initial_search_query_count: initial_search_query_count,
          max_research_loops: max_research_loops,
          reasoning_model: model,
        });
      }
    },
    [currentThread, activeTab, powerPlantThread, generalThread]
  );

  const handleCancel = useCallback(() => {
    currentThread.stop();
    window.location.reload();
  }, [currentThread]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setProcessedEventsTimeline([]);
    setError(null);
  };

  return (
    <div className="flex h-screen bg-neutral-800 text-neutral-100 font-sans antialiased">
      <main className="h-full w-full max-w-5xl mx-auto">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="h-full flex flex-col">
          <div className="border-b border-neutral-700 px-4 py-2">
            <TabsList className="grid w-full max-w-md grid-cols-2 bg-neutral-700">
              <TabsTrigger value="power-plant" className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Power Plant Search
              </TabsTrigger>
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                General Research
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="power-plant" className="flex-1 mt-0">
            {currentThread.messages.length === 0 ? (
              <PowerPlantWelcomeScreen
                handleSubmit={handleSubmit}
                isLoading={currentThread.isLoading}
                onCancel={handleCancel}
              />
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="flex flex-col items-center justify-center gap-4">
                  <h1 className="text-2xl text-red-400 font-bold">Error</h1>
                  <p className="text-red-400">{JSON.stringify(error)}</p>
                  <Button
                    variant="destructive"
                    onClick={() => window.location.reload()}
                  >
                    Retry
                  </Button>
                </div>
              </div>
            ) : (
              <ChatMessagesView
                messages={currentThread.messages}
                isLoading={currentThread.isLoading}
                scrollAreaRef={scrollAreaRef}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                liveActivityEvents={processedEventsTimeline}
                historicalActivities={historicalActivities}
              />
            )}
          </TabsContent>

          <TabsContent value="general" className="flex-1 mt-0">
            <div className="flex flex-col items-center justify-center h-full">
              <div className="text-center space-y-4">
                <Search className="h-16 w-16 text-neutral-400 mx-auto" />
                <h2 className="text-2xl font-semibold text-neutral-100">
                  General Research Agent
                </h2>
                <p className="text-neutral-400 max-w-md">
                  This tab would contain the general research functionality. 
                  For now, please use the Power Plant Search tab for specialized power plant annual report searches.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
