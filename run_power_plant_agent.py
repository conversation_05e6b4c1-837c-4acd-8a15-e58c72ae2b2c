#!/usr/bin/env python3
"""
Simple script to run the Power Plant LangGraph Agent via API

This script demonstrates how to use the specialized power plant agent
through the LangGraph server API.
"""

import requests
import json
import time
import sys

def run_power_plant_search(plant_name: str, api_url: str = "http://localhost:2024"):
    """
    Run power plant search using the LangGraph API.
    
    Args:
        plant_name: Name of the power plant to search for
        api_url: URL of the LangGraph server
    """
    print(f"🔍 Searching for: {plant_name}")
    print(f"🌐 API URL: {api_url}")
    print("-" * 50)
    
    # Prepare the request
    thread_data = {
        "messages": [
            {
                "type": "human",
                "content": plant_name
            }
        ]
    }
    
    try:
        # Start the agent run
        response = requests.post(
            f"{api_url}/threads",
            json=thread_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ Error creating thread: {response.status_code}")
            print(response.text)
            return
        
        thread_id = response.json()["thread_id"]
        print(f"✅ Created thread: {thread_id}")
        
        # Run the power plant agent
        run_data = {
            "assistant_id": "power-plant-agent",
            "input": thread_data
        }
        
        response = requests.post(
            f"{api_url}/threads/{thread_id}/runs",
            json=run_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ Error starting run: {response.status_code}")
            print(response.text)
            return
        
        run_id = response.json()["run_id"]
        print(f"🚀 Started run: {run_id}")
        
        # Poll for completion
        print("⏳ Waiting for completion...")
        while True:
            response = requests.get(f"{api_url}/threads/{thread_id}/runs/{run_id}")
            
            if response.status_code != 200:
                print(f"❌ Error checking run status: {response.status_code}")
                break
            
            run_status = response.json()
            status = run_status.get("status", "unknown")
            
            print(f"📊 Status: {status}")
            
            if status == "completed":
                print("✅ Run completed!")
                break
            elif status == "failed":
                print("❌ Run failed!")
                print(json.dumps(run_status, indent=2))
                break
            elif status in ["cancelled", "expired"]:
                print(f"⚠️  Run {status}")
                break
            
            time.sleep(2)  # Wait 2 seconds before checking again
        
        # Get the final messages
        response = requests.get(f"{api_url}/threads/{thread_id}/messages")
        
        if response.status_code == 200:
            messages = response.json()["messages"]
            print("\n📝 Final Response:")
            print("=" * 50)
            
            for message in messages:
                if message["type"] == "ai":
                    print(message["content"])
                    break
        else:
            print(f"❌ Error getting messages: {response.status_code}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Error: Cannot connect to LangGraph server")
        print("   Make sure the server is running with: langgraph up")
    except Exception as e:
        print(f"❌ Error: {e}")


def main():
    """Main function."""
    print("🏭 Power Plant Annual Report Agent")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # Use command line argument
        plant_name = " ".join(sys.argv[1:])
    else:
        # Interactive mode
        plant_name = input("Enter power plant name: ").strip()
    
    if not plant_name:
        print("❌ No plant name provided")
        return
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:2024/health")
        if response.status_code == 200:
            print("✅ LangGraph server is running")
        else:
            print("⚠️  LangGraph server responded with unexpected status")
    except requests.exceptions.ConnectionError:
        print("❌ LangGraph server is not running")
        print("   Start it with: cd backend && langgraph up")
        return
    
    # Run the search
    run_power_plant_search(plant_name)


if __name__ == "__main__":
    main()
